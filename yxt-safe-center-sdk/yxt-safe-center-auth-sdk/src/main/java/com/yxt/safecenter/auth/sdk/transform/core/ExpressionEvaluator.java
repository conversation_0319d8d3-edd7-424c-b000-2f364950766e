package com.yxt.safecenter.auth.sdk.transform.core;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yxt.safecenter.auth.sdk.transform.config.SystemParameterConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表达式求值器
 * 支持解析和计算各种表达式
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExpressionEvaluator {

    private final SystemParameterConfig systemParameterConfig;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 匹配 ${expression} 格式的表达式
    private static final Pattern EXPRESSION_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    
    // 匹配 defaultEmpty(field, defaultValue) 函数
    private static final Pattern DEFAULT_EMPTY_PATTERN = Pattern.compile("^\\s*defaultEmpty\\(([^,]+?)\\s*,\\s*([^,\\)]+)\\)\\s*$");

    // 匹配三元运算符 field ? value1 : value2
    private static final Pattern TERNARY_PATTERN = Pattern.compile("([^?]+)\\s*\\?\\s*([^:]+)\\s*:\\s*(.+)");
    
    /**
     * 评估表达式并返回结果
     * 
     * @param expression 表达式字符串
     * @param exchange 服务器交换对象
     * @param requestBody 请求体JSON
     * @return 评估结果
     */
    public Object evaluateExpression(String expression, ServerWebExchange exchange, JsonNode requestBody) {
        if (!StringUtils.hasText(expression)) {
            return null;
        }
        
        try {
            // 处理复杂表达式 ${...}
            Matcher expressionMatcher = EXPRESSION_PATTERN.matcher(expression);
            if (expressionMatcher.matches()) {
                String innerExpression = expressionMatcher.group(1);
                return evaluateInnerExpression(innerExpression, exchange, requestBody);
            }
            
            // 处理简单表达式
            return evaluateSimpleExpression(expression, exchange, requestBody);
            
        } catch (Exception e) {
            log.warn("表达式求值失败: {}", expression, e);
            return null;
        }
    }
    
    /**
     * 评估内部表达式（去掉${}后的表达式）
     */
    private Object evaluateInnerExpression(String expression, ServerWebExchange exchange, JsonNode requestBody) {
        // 处理 defaultEmpty 函数
        Matcher defaultEmptyMatcher = DEFAULT_EMPTY_PATTERN.matcher(expression);
        if (defaultEmptyMatcher.matches()) {
            String fieldExpression = defaultEmptyMatcher.group(1).trim();
            String defaultValueExpression = defaultEmptyMatcher.group(2);
            
            Object fieldValue = evaluateSimpleExpression(fieldExpression, exchange, requestBody);
            return (fieldValue == null || "".equals(fieldValue)) ? evaluateSimpleExpression(defaultValueExpression, exchange, requestBody) : fieldValue;
        }
        
        // 处理三元运算符
        Matcher ternaryMatcher = TERNARY_PATTERN.matcher(expression);
        if (ternaryMatcher.matches()) {
            String conditionExpression = ternaryMatcher.group(1).trim();
            String trueValue = ternaryMatcher.group(2).trim();
            String falseValue = ternaryMatcher.group(3).trim();
            
            Object conditionResult = evaluateSimpleExpression(conditionExpression, exchange, requestBody);
            boolean condition = isTrue(conditionResult);
            
            return condition ? parseValue(trueValue) : parseValue(falseValue);
        }
        
        // 其他情况按简单表达式处理
        return evaluateSimpleExpression(expression, exchange, requestBody);
    }
    
    /**
     * 评估简单表达式
     */
    private Object evaluateSimpleExpression(String expression, ServerWebExchange exchange, JsonNode requestBody) {
        if (expression.startsWith("const.")) {
            // 常量值
            return expression.substring(6);
        } else if (expression.startsWith("sys.")) {
            // 系统参数，从请求头获取
            String sysParam = expression.substring(4);
            return getSystemParameter(sysParam, exchange);
        } else if (expression.startsWith("header.")) {
            // 请求头参数
            String headerName = expression.substring(7);
            return getHeaderValue(headerName, exchange);
        } else if (expression.startsWith("query.")) {
            // 查询参数
            String queryName = expression.substring(6);
            return getQueryValue(queryName, exchange);
        } else if (expression.startsWith("body.")) {
            // 请求体参数
            String bodyPath = expression.substring(5);
            return getBodyValue(bodyPath, requestBody);
        }
        
        return expression;
    }
    
    /**
     * 获取系统参数
     */
    private Object getSystemParameter(String paramName, ServerWebExchange exchange) {
        // 从配置文件获取系统参数
        String paramValue = systemParameterConfig.getParameter(paramName);
        if (paramValue != null) {
            return paramValue;
        }

        // 如果配置文件中没有找到，记录警告日志
        log.warn("系统参数 '{}' 在配置文件中未找到", paramName);
        return null;
    }
    
    /**
     * 获取请求头值
     */
    private Object getHeaderValue(String headerName, ServerWebExchange exchange) {
        HttpHeaders headers = exchange.getRequest().getHeaders();
        return headers.getFirst(headerName);
    }
    
    /**
     * 获取查询参数值
     */
    private Object getQueryValue(String queryName, ServerWebExchange exchange) {
        MultiValueMap<String, String> queryParams = exchange.getRequest().getQueryParams();
        return queryParams.getFirst(queryName);
    }
    
    /**
     * 获取请求体中的值
     */
    private Object getBodyValue(String path, JsonNode requestBody) {
        if (requestBody == null) {
            return null;
        }

        String[] pathParts = path.split("\\.");
        JsonNode current = requestBody;

        for (String part : pathParts) {
            if (current == null || !current.has(part)) {
                return null;
            }
            current = current.get(part);
        }

        if (current.isTextual()) {
            return current.asText();
        } else if (current.isNumber()) {
            return current.asLong();
        } else if (current.isBoolean()) {
            return current.asBoolean();
        } else if (current.isObject() || current.isArray()) {
            // 如果是对象或数组，直接返回JsonNode对象
            return current;
        } else {
            return current.toString();
        }
    }
    
    /**
     * 判断值是否为真
     */
    private boolean isTrue(Object value) {
        if (value == null) {
            return false;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            String str = (String) value;
            return !"".equals(str) && !"false".equalsIgnoreCase(str) && !"0".equals(str);
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue() != 0;
        }
        return true;
    }
    
    /**
     * 解析值（去掉引号等）
     */
    private Object parseValue(String value) {
        if (value == null) {
            return null;
        }
        
        value = value.trim();
        
        // 去掉引号
        if ((value.startsWith("\"") && value.endsWith("\"")) || 
            (value.startsWith("'") && value.endsWith("'"))) {
            return value.substring(1, value.length() - 1);
        }
        
        // 尝试解析为数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            // 不是数字，返回字符串
            return value;
        }
    }
}
