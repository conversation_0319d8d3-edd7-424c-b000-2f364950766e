package com.yxt.safecenter.auth.sdk.transform.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统参数配置类
 * 用于管理请求和响应转换时使用的系统参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-09
 */
@Data
@Component
@ConfigurationProperties(prefix = "safe-center.transform.system-param")
public class SystemParameterConfig {
    
    /**
     * 系统参数映射
     * key: 参数名称
     * value: 参数值
     */
    private Map<String, String> parameters = new HashMap<>();
    
    /**
     * 获取系统参数值
     * 
     * @param paramName 参数名称
     * @return 参数值，如果不存在则返回null
     */
    public String getParameter(String paramName) {
        return parameters.get(paramName);
    }

    /**
     * 检查参数是否存在
     * 
     * @param paramName 参数名称
     * @return 是否存在
     */
    public boolean hasParameter(String paramName) {
        return parameters.containsKey(paramName);
    }
    
    /**
     * 获取所有参数
     * 
     * @return 参数映射
     */
    public Map<String, String> getAllParameters() {
        return new HashMap<>(parameters);
    }
}
