package com.yxt.safecenter.auth.sdk.transform.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 响应参数映射配置
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseMappingConfig {
    
    /**
     * 响应映射规则
     * key: 目标字段名, value: 源字段表达式
     */
    private Map<String, String> mapping;
}
