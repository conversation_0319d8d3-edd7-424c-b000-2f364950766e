//package com.yxt.safecenter.auth.sdk.core.transform.filter;
//
//import com.yxt.safecenter.auth.sdk.core.transform.config.ParameterMappingConfig;
//import com.yxt.safecenter.auth.sdk.core.transform.config.ParameterMappingConfigManager;
//import com.yxt.safecenter.auth.sdk.core.transform.core.ParameterTransformer;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.reactivestreams.Publisher;
//import org.springframework.cloud.gateway.filter.GatewayFilter;
//import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
//import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
//import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
//import org.springframework.core.Ordered;
//import org.springframework.core.io.buffer.DataBuffer;
//import org.springframework.core.io.buffer.DataBufferFactory;
//import org.springframework.core.io.buffer.DataBufferUtils;
//import org.springframework.http.server.reactive.ServerHttpResponse;
//import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
//import org.springframework.stereotype.Component;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//import java.net.URI;
//import java.nio.charset.StandardCharsets;
//
///**
// * 参数转换网关过滤器工厂
// *
// * <AUTHOR> Assistant
// * @since 2025-07-24
// */
//@Slf4j
//@Component
//public class ParameterTransformGatewayFilterFactory extends AbstractGatewayFilterFactory<ParameterTransformGatewayFilterFactory.Config> {
//
//    private final ParameterMappingConfigManager configManager;
//    private final ParameterTransformer parameterTransformer;
//
//    public ParameterTransformGatewayFilterFactory(ParameterMappingConfigManager configManager,
//                                                 ParameterTransformer parameterTransformer) {
//        super(Config.class);
//        this.configManager = configManager;
//        this.parameterTransformer = parameterTransformer;
//    }
//
//    @Override
//    public GatewayFilter apply(Config config) {
//        GatewayFilter filter = (exchange, chain) -> {
//            if (!configManager.isTransformEnabled()) {
//                return chain.filter(exchange);
//            }
//
//            String path = exchange.getRequest().getPath().pathWithinApplication().value();
//            ParameterMappingConfig mappingConfig = configManager.getConfig(path);
//            URI requestUrl = (URI) exchange.getAttributes().get(ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR);
//            if (requestUrl != null) {
//                path = requestUrl.getPath();
//            }
//            if (mappingConfig == null) {
//                return chain.filter(exchange);
//            }
//
//            log.debug("应用参数转换配置到路径: {}", path);
//
//            // 转换请求参数
//            return parameterTransformer.transformRequest(exchange, mappingConfig)
//                .flatMap(transformedExchange -> {
//                    log.debug("开始包装响应以进行参数转换");
//                    // 包装响应以转换响应参数
//                    ServerHttpResponse originalResponse = transformedExchange.getResponse();
//                    DataBufferFactory bufferFactory = originalResponse.bufferFactory();
//
//                    ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(originalResponse) {
//                        @Override
//                        public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
//                            log.debug("writeWith被调用，开始响应转换");
//
//                            return super.writeWith(
//                                Flux.from(body)
//                                    .collectList()
//                                    .doOnNext(dataBuffers -> log.debug("收集到 {} 个数据缓冲区", dataBuffers.size()))
//                                    .flatMapMany(dataBuffers -> {
//                                        if (dataBuffers.isEmpty()) {
//                                            log.debug("响应体为空，跳过转换");
//                                            return Flux.empty();
//                                        }
//
//                                        try {
//                                            // 读取响应体内容
//                                            StringBuilder responseBodyBuilder = new StringBuilder();
//                                            for (DataBuffer buffer : dataBuffers) {
//                                                byte[] bytes = new byte[buffer.readableByteCount()];
//                                                buffer.read(bytes);
//                                                responseBodyBuilder.append(new String(bytes, StandardCharsets.UTF_8));
//                                            }
//
//                                            String originalResponseStr = responseBodyBuilder.toString();
//                                            log.debug("读取到原始响应体: {}", originalResponseStr);
//
//                                            // 如果响应体为空或不是JSON，直接返回原始数据
//                                            if (originalResponseStr.trim().isEmpty() || !originalResponseStr.trim().startsWith("{")) {
//                                                log.debug("响应体为空或非JSON格式，跳过转换");
//                                                // 释放原始buffers并重新创建
//                                                dataBuffers.forEach(DataBufferUtils::release);
//                                                return Flux.just(bufferFactory.wrap(originalResponseStr.getBytes(StandardCharsets.UTF_8)));
//                                            }
//
//                                            // 执行响应转换
//                                            return parameterTransformer.transformResponse(originalResponseStr, mappingConfig, transformedExchange)
//                                                .doOnNext(transformed -> log.debug("响应转换完成: {}", transformed))
//                                                .map(transformedResponseStr -> {
//                                                    // 释放原始buffers
//                                                    dataBuffers.forEach(DataBufferUtils::release);
//                                                    return bufferFactory.wrap(transformedResponseStr.getBytes(StandardCharsets.UTF_8));
//                                                })
//                                                .onErrorResume(throwable -> {
//                                                    log.error("响应转换失败，使用原始响应", throwable);
//                                                    // 释放原始buffers
//                                                    dataBuffers.forEach(DataBufferUtils::release);
//                                                    return Mono.just(bufferFactory.wrap(originalResponseStr.getBytes(StandardCharsets.UTF_8)));
//                                                })
//                                                .flux();
//
//                                        } catch (Exception e) {
//                                            log.error("读取响应体时发生错误", e);
//                                            // 释放buffers
//                                            dataBuffers.forEach(DataBufferUtils::release);
//                                            return Flux.error(e);
//                                        }
//                                    })
//                            );
//                        }
//                    };
//
//                    return chain.filter(transformedExchange.mutate().response(decoratedResponse).build());
//                })
//                .onErrorResume(throwable -> {
//                    log.error("参数转换过程中发生错误", throwable);
//                    return chain.filter(exchange);
//                });
//        };
//
//        // 设置过滤器执行顺序，确保在其他过滤器之前执行
//        return new OrderedGatewayFilter(filter, Ordered.HIGHEST_PRECEDENCE + 1000);
//    }
//
//    @Data
//    public static class Config {
//        // 可以添加一些配置参数，比如是否启用、优先级等
//        private boolean enabled = true;
//        private String configPath;
//    }
//}
