package com.yxt.safecenter.auth.sdk.transform.filter;

import com.alibaba.fastjson.JSON;
import com.yxt.safecenter.auth.sdk.transform.config.ParameterMappingConfig;
import com.yxt.safecenter.auth.sdk.transform.core.ParameterTransformer;
import com.yxt.safecenter.auth.sdk.dto.InterfaceInfoDTO;
import com.yxt.safecenter.auth.sdk.service.SafeCenterDataService;
import com.yxt.safecenter.auth.sdk.utils.HttpUtils;
import com.yxt.safecenter.auth.sdk.utils.InterfaceMatcherUtil;
import com.yxt.safecenter.common.model.enums.InterfaceStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reactivestreams.Publisher;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 参数转换全局过滤器
 * 自动对所有请求和响应进行参数转换处理
 * 
 * <AUTHOR>
 * @since 2025-08-09
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ParameterTransformGlobalFilter implements GlobalFilter, Ordered {

    private final ParameterTransformer parameterTransformer;
    private final SafeCenterDataService safeCenterDataService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        // 获取请求路径
        String path = exchange.getRequest().getPath().pathWithinApplication().value();
        URI requestUrl = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR);
        if (requestUrl != null) {
            path = requestUrl.getPath();
        } else {
            path = HttpUtils.getGatewayStripPrefixPath(path);
        }
        // 获取并匹配接口信息
        Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
        if (route == null) {
            return chain.filter(exchange);
        }
        String applicationName = route.getUri().getHost();
        ServerHttpRequest request = exchange.getRequest();
        String method = request.getMethodValue();
        List<InterfaceInfoDTO> interfaceInfoList = safeCenterDataService.getInterfaceList(applicationName);
        InterfaceInfoDTO interfaceInfoDTO = InterfaceMatcherUtil.matchApi(interfaceInfoList, path, method);
        // 没有接口信息 没发布 没配置转换规则
        if (interfaceInfoDTO == null || !InterfaceStatusEnum.ONLINE.name().equals(interfaceInfoDTO.getStatus())
                || StringUtils.isEmpty(interfaceInfoDTO.getTransformRules())) {
            return chain.filter(exchange);
        }

        ParameterMappingConfig mappingConfig = null;
        try {
            mappingConfig = JSON.parseObject(interfaceInfoDTO.getTransformRules(), ParameterMappingConfig.class);
        } catch (Exception e) {
            log.warn("参数转换-转换配置反序列化异常", e);
        }
        if (mappingConfig == null) {
            return chain.filter(exchange);
        }

        // 转换请求参数
        ParameterMappingConfig finalMappingConfig = mappingConfig;
        return parameterTransformer.transformRequest(exchange, mappingConfig)
            .flatMap(transformedExchange -> {
                log.debug("开始包装响应以进行参数转换");
                
                // 包装响应以转换响应参数
                ServerHttpResponse originalResponse = transformedExchange.getResponse();
                DataBufferFactory bufferFactory = originalResponse.bufferFactory();

                ServerHttpResponseDecorator decoratedResponse = new ServerHttpResponseDecorator(originalResponse) {
                    @Override
                    public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                        log.debug("writeWith被调用，开始响应转换");

                        return super.writeWith(
                            Flux.from(body)
                                .collectList()
                                .doOnNext(dataBuffers -> log.debug("收集到 {} 个数据缓冲区", dataBuffers.size()))
                                .flatMapMany(dataBuffers -> {
                                    if (dataBuffers.isEmpty()) {
                                        log.debug("响应体为空，跳过转换");
                                        return Flux.empty();
                                    }

                                    try {
                                        // 读取响应体内容
                                        StringBuilder responseBodyBuilder = new StringBuilder();
                                        for (DataBuffer buffer : dataBuffers) {
                                            byte[] bytes = new byte[buffer.readableByteCount()];
                                            buffer.read(bytes);
                                            responseBodyBuilder.append(new String(bytes, StandardCharsets.UTF_8));
                                        }

                                        String originalResponseStr = responseBodyBuilder.toString();
                                        log.debug("读取到原始响应体: {}", originalResponseStr);

                                        // 如果响应体为空或不是JSON，直接返回原始数据
                                        if (originalResponseStr.trim().isEmpty() || !originalResponseStr.trim().startsWith("{")) {
                                            log.warn("响应体为空或非JSON格式，跳过转换");
                                            // 释放原始buffers并重新创建
                                            dataBuffers.forEach(DataBufferUtils::release);
                                            return Flux.just(bufferFactory.wrap(originalResponseStr.getBytes(StandardCharsets.UTF_8)));
                                        }

                                        // 执行响应转换
                                        return parameterTransformer.transformResponse(originalResponseStr, finalMappingConfig, transformedExchange)
                                            .doOnNext(transformed -> log.debug("响应转换完成: {}", transformed))
                                            .map(transformedResponseStr -> {
                                                // 释放原始buffers
                                                dataBuffers.forEach(DataBufferUtils::release);
                                                return bufferFactory.wrap(transformedResponseStr.getBytes(StandardCharsets.UTF_8));
                                            })
                                            .onErrorResume(throwable -> {
                                                log.error("响应转换失败，使用原始响应", throwable);
                                                // 释放原始buffers
                                                dataBuffers.forEach(DataBufferUtils::release);
                                                return Mono.just(bufferFactory.wrap(originalResponseStr.getBytes(StandardCharsets.UTF_8)));
                                            })
                                            .flux();

                                    } catch (Exception e) {
                                        log.error("读取响应体时发生错误", e);
                                        // 释放buffers
                                        dataBuffers.forEach(DataBufferUtils::release);
                                        return Flux.error(e);
                                    }
                                })
                        );
                    }
                };

                return chain.filter(transformedExchange.mutate().response(decoratedResponse).build());
            })
            .onErrorResume(throwable -> {
                log.error("参数转换过程中发生错误", throwable);
                return chain.filter(exchange);
            });
    }

    @Override
    public int getOrder() {
        // 设置较高的优先级，确保在其他过滤器之前执行
        // 但不是最高优先级，避免影响其他重要的全局过滤器
        return -100;
    }
}
