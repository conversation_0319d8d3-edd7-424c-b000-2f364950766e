package com.yxt.safecenter.common.model.utils;

import org.junit.Test;

/**
 * DSL映射转换工具类测试
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
public class DslMappingUtilTest {

    @Test
    public void testConvertToDslExpression() {
        // 测试入参
        String requestParams = "{\n" +
                "  \"map\": {\n" +
                "    \"in\": \"body\",\n" +
                "    \"type\": \"object\"\n" +
                "  },\n" +
                "  \"map1\": {\n" +
                "    \"in\": \"body\",\n" +
                "    \"type\": \"object\",\n" +
                "    \"properties\": {\n" +
                "      \"channel\": {\n" +
                "        \"type\": \"string\",\n" +
                "        \"required\": false,\n" +
                "        \"description\": \"渠道\"\n" +
                "      },\n" +
                "      \"demoReqs\": {\n" +
                "        \"type\": \"array\",\n" +
                "        \"items\": {\n" +
                "          \"type\": \"string\"\n" +
                "        },\n" +
                "        \"required\": false\n" +
                "      }\n" +
                "    },\n" +
                "    \"description\": \"OpenApi-会员积分扣减/返还参数\"\n" +
                "  },\n" +
                "  \"scene\": {\n" +
                "    \"in\": \"query\",\n" +
                "    \"type\": \"string\",\n" +
                "    \"required\": false,\n" +
                "    \"description\": \"\"\n" +
                "  },\n" +
                "  \"scene1\": {\n" +
                "    \"in\": \"query\",\n" +
                "    \"type\": \"string\",\n" +
                "    \"required\": false,\n" +
                "    \"description\": \"\"\n" +
                "  },\n" +
                "  \"headerTest\": {\n" +
                "    \"in\": \"header\",\n" +
                "    \"type\": \"string\",\n" +
                "    \"required\": true,\n" +
                "    \"description\": \"\"\n" +
                "  },\n" +
                "  \"headerTest1\": {\n" +
                "    \"in\": \"header\",\n" +
                "    \"type\": \"string\",\n" +
                "    \"required\": false,\n" +
                "    \"description\": \"\"\n" +
                "  }\n" +
                "}";

        // 测试出参
        String responseParams = "{\n" +
                "  \"resp\": {\n" +
                "    \"type\": \"object\",\n" +
                "    \"properties\": {\n" +
                "      \"msg\": {\n" +
                "        \"type\": \"string\",\n" +
                "        \"required\": false,\n" +
                "        \"description\": \"请求返回信息\"\n" +
                "      },\n" +
                "      \"code\": {\n" +
                "        \"type\": \"string\",\n" +
                "        \"required\": false,\n" +
                "        \"description\": \"请求返回码，10000-成功，其他失败\"\n" +
                "      },\n" +
                "      \"data\": {\n" +
                "        \"type\": \"object\",\n" +
                "        \"required\": false,\n" +
                "        \"description\": \"请求返回实体对象\",\n" +
                "        \"properties\": {\n" +
                "          \"channel\": {\n" +
                "            \"type\": \"string\",\n" +
                "            \"required\": false,\n" +
                "            \"description\": \"渠道\"\n" +
                "          }\n" +
                "        }\n" +
                "      },\n" +
                "      \"subCode\": {\n" +
                "        \"type\": \"string\",\n" +
                "        \"required\": false,\n" +
                "        \"description\": \"子请求返回码\"\n" +
                "      },\n" +
                "      \"traceId\": {\n" +
                "        \"type\": \"string\",\n" +
                "        \"required\": false,\n" +
                "        \"description\": \"链路id\"\n" +
                "      },\n" +
                "      \"timestamp\": {\n" +
                "        \"type\": \"integer\",\n" +
                "        \"required\": false\n" +
                "      },\n" +
                "      \"subMessage\": {\n" +
                "        \"type\": \"string\",\n" +
                "        \"required\": false,\n" +
                "        \"description\": \"子响应消息\"\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";

        // 转换为DSL表达式
        String dslExpression = DslMappingUtil.convertToDslExpression(requestParams, responseParams);
        System.out.println("DSL表达式:");
        System.out.println(dslExpression);
        
        // 测试单独构建请求映射
        System.out.println("\n请求映射:");
        String requestDsl = DslMappingUtil.buildRequestDslMapping(requestParams);
        System.out.println(requestDsl);
        
        // 测试单独构建响应映射
        System.out.println("\n响应映射:");
        String responseDsl = DslMappingUtil.buildResponseDslMapping(responseParams);
        System.out.println(responseDsl);
    }

    @Test
    public void testComplexNestedObject() {
        // 测试复杂嵌套对象
        String complexRequest = "{\n" +
                "  \"complexObj\": {\n" +
                "    \"in\": \"body\",\n" +
                "    \"type\": \"object\",\n" +
                "    \"properties\": {\n" +
                "      \"level1\": {\n" +
                "        \"type\": \"object\",\n" +
                "        \"properties\": {\n" +
                "          \"level2\": {\n" +
                "            \"type\": \"object\",\n" +
                "            \"properties\": {\n" +
                "              \"level3Field\": {\n" +
                "                \"type\": \"string\",\n" +
                "                \"description\": \"三级字段\"\n" +
                "              }\n" +
                "            }\n" +
                "          },\n" +
                "          \"level2Field\": {\n" +
                "            \"type\": \"string\",\n" +
                "            \"description\": \"二级字段\"\n" +
                "          }\n" +
                "        }\n" +
                "      },\n" +
                "      \"level1Field\": {\n" +
                "        \"type\": \"string\",\n" +
                "        \"description\": \"一级字段\"\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";

        String complexResponse = "{\n" +
                "  \"result\": {\n" +
                "    \"type\": \"object\",\n" +
                "    \"properties\": {\n" +
                "      \"data\": {\n" +
                "        \"type\": \"object\",\n" +
                "        \"properties\": {\n" +
                "          \"user\": {\n" +
                "            \"type\": \"object\",\n" +
                "            \"properties\": {\n" +
                "              \"name\": {\n" +
                "                \"type\": \"string\"\n" +
                "              },\n" +
                "              \"age\": {\n" +
                "                \"type\": \"integer\"\n" +
                "              }\n" +
                "            }\n" +
                "          }\n" +
                "        }\n" +
                "      },\n" +
                "      \"status\": {\n" +
                "        \"type\": \"string\"\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";

        String dslExpression = DslMappingUtil.convertToDslExpression(complexRequest, complexResponse);
        System.out.println("复杂嵌套对象DSL表达式:");
        System.out.println(dslExpression);
    }

    public static void main(String[] args) {
        DslMappingUtilTest test = new DslMappingUtilTest();
        test.testConvertToDslExpression();
        System.out.println("\n" + "=".repeat(50) + "\n");
        test.testComplexNestedObject();
    }
}
