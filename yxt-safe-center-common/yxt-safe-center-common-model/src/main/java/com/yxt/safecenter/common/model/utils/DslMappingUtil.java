package com.yxt.safecenter.common.model.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * DSL映射转换工具类
 * 用于将入参和出参转换为默认的DSL表达式
 * 
 * <AUTHOR>
 * @since 2025-01-03
 */
public class DslMappingUtil {

    /**
     * 将入参和出参转换为DSL表达式
     * 
     * @param requestParamsJson 请求参数JSON字符串
     * @param responseParamsJson 响应参数JSON字符串
     * @return DSL表达式JSON字符串
     */
    public static String convertToDslExpression(String requestParamsJson, String responseParamsJson) {
        Map<String, Object> dslExpression = new LinkedHashMap<>();
        
        // 处理请求参数
        if (StringUtils.isNotEmpty(requestParamsJson)) {
            Map<String, Object> requestMapping = buildRequestMapping(requestParamsJson);
            dslExpression.put("request", requestMapping);
        }
        
        // 处理响应参数
        if (StringUtils.isNotEmpty(responseParamsJson)) {
            Map<String, Object> responseMapping = buildResponseMapping(responseParamsJson);
            dslExpression.put("response", responseMapping);
        }
        
        return JSON.toJSONString(dslExpression, true);
    }

    /**
     * 构建请求参数映射
     * 
     * @param requestParamsJson 请求参数JSON字符串
     * @return 请求参数映射结构
     */
    private static Map<String, Object> buildRequestMapping(String requestParamsJson) {
        Map<String, Object> requestMapping = new LinkedHashMap<>();
        Map<String, Object> mapping = new LinkedHashMap<>();
        
        try {
            JSONObject requestParams = JSON.parseObject(requestParamsJson);
            
            // 按in字段分组
            Map<String, Map<String, String>> groupedMappings = new LinkedHashMap<>();
            
            for (Map.Entry<String, Object> entry : requestParams.entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                
                if (paramValue instanceof JSONObject) {
                    JSONObject paramObj = (JSONObject) paramValue;
                    String inValue = paramObj.getString("in");
                    String type = paramObj.getString("type");
                    
                    if (StringUtils.isEmpty(inValue)) {
                        continue;
                    }
                    
                    groupedMappings.computeIfAbsent(inValue, k -> new LinkedHashMap<>());
                    
                    if ("object".equals(type) && paramObj.containsKey("properties")) {
                        // 处理对象类型参数，递归展开所有字段
                        JSONObject properties = paramObj.getJSONObject("properties");
                        processObjectProperties(groupedMappings.get(inValue), properties, inValue, "");
                    } else {
                        // 处理基本类型参数
                        groupedMappings.get(inValue).put(paramName, inValue + "." + paramName);
                    }
                }
            }
            
            // 将分组后的映射添加到mapping中
            for (Map.Entry<String, Map<String, String>> groupEntry : groupedMappings.entrySet()) {
                if (!groupEntry.getValue().isEmpty()) {
                    mapping.put(groupEntry.getKey(), groupEntry.getValue());
                }
            }
            
        } catch (Exception e) {
            throw new RuntimeException("构建请求参数映射失败: " + e.getMessage(), e);
        }
        
        requestMapping.put("mapping", mapping);
        return requestMapping;
    }

    /**
     * 递归处理对象属性
     * 
     * @param targetMapping 目标映射
     * @param properties 对象属性
     * @param inValue 参数位置
     * @param parentPath 父级路径
     */
    private static void processObjectProperties(Map<String, String> targetMapping, 
                                              JSONObject properties, 
                                              String inValue, 
                                              String parentPath) {
        for (Map.Entry<String, Object> propEntry : properties.entrySet()) {
            String propName = propEntry.getKey();
            Object propValue = propEntry.getValue();
            
            if (propValue instanceof JSONObject) {
                JSONObject propObj = (JSONObject) propValue;
                String propType = propObj.getString("type");
                
                String currentPath = StringUtils.isEmpty(parentPath) ? propName : parentPath + "." + propName;
                
                if ("object".equals(propType) && propObj.containsKey("properties")) {
                    // 递归处理嵌套对象
                    JSONObject nestedProperties = propObj.getJSONObject("properties");
                    processObjectProperties(targetMapping, nestedProperties, inValue, currentPath);
                } else {
                    // 基本类型或数组类型
                    targetMapping.put(currentPath, inValue + "." + currentPath);
                }
            }
        }
    }

    /**
     * 构建响应参数映射
     * 
     * @param responseParamsJson 响应参数JSON字符串
     * @return 响应参数映射结构
     */
    private static Map<String, Object> buildResponseMapping(String responseParamsJson) {
        Map<String, Object> responseMapping = new LinkedHashMap<>();
        Map<String, String> mapping = new LinkedHashMap<>();
        
        try {
            JSONObject responseParams = JSON.parseObject(responseParamsJson);
            
            for (Map.Entry<String, Object> entry : responseParams.entrySet()) {
                String paramName = entry.getKey();
                Object paramValue = entry.getValue();
                
                if (paramValue instanceof JSONObject) {
                    JSONObject paramObj = (JSONObject) paramValue;
                    String type = paramObj.getString("type");
                    
                    if ("object".equals(type) && paramObj.containsKey("properties")) {
                        // 处理对象类型响应，递归展开所有字段
                        JSONObject properties = paramObj.getJSONObject("properties");
                        processResponseObjectProperties(mapping, properties, "");
                    } else {
                        // 处理基本类型响应
                        mapping.put(paramName, paramName);
                    }
                }
            }
            
        } catch (Exception e) {
            throw new RuntimeException("构建响应参数映射失败: " + e.getMessage(), e);
        }
        
        responseMapping.put("mapping", mapping);
        return responseMapping;
    }

    /**
     * 递归处理响应对象属性
     * 
     * @param targetMapping 目标映射
     * @param properties 对象属性
     * @param parentPath 父级路径
     */
    private static void processResponseObjectProperties(Map<String, String> targetMapping, 
                                                      JSONObject properties, 
                                                      String parentPath) {
        for (Map.Entry<String, Object> propEntry : properties.entrySet()) {
            String propName = propEntry.getKey();
            Object propValue = propEntry.getValue();
            
            if (propValue instanceof JSONObject) {
                JSONObject propObj = (JSONObject) propValue;
                String propType = propObj.getString("type");
                
                String currentPath = StringUtils.isEmpty(parentPath) ? propName : parentPath + "." + propName;
                
                if ("object".equals(propType) && propObj.containsKey("properties")) {
                    // 递归处理嵌套对象
                    JSONObject nestedProperties = propObj.getJSONObject("properties");
                    processResponseObjectProperties(targetMapping, nestedProperties, currentPath);
                } else {
                    // 基本类型或数组类型
                    targetMapping.put(currentPath, currentPath);
                }
            }
        }
    }

    /**
     * 仅构建请求参数映射
     * 
     * @param requestParamsJson 请求参数JSON字符串
     * @return 请求参数DSL映射JSON字符串
     */
    public static String buildRequestDslMapping(String requestParamsJson) {
        if (StringUtils.isEmpty(requestParamsJson)) {
            return "{}";
        }
        
        Map<String, Object> dslExpression = new LinkedHashMap<>();
        Map<String, Object> requestMapping = buildRequestMapping(requestParamsJson);
        dslExpression.put("request", requestMapping);
        
        return JSON.toJSONString(dslExpression, true);
    }

    /**
     * 仅构建响应参数映射
     * 
     * @param responseParamsJson 响应参数JSON字符串
     * @return 响应参数DSL映射JSON字符串
     */
    public static String buildResponseDslMapping(String responseParamsJson) {
        if (StringUtils.isEmpty(responseParamsJson)) {
            return "{}";
        }
        
        Map<String, Object> dslExpression = new LinkedHashMap<>();
        Map<String, Object> responseMapping = buildResponseMapping(responseParamsJson);
        dslExpression.put("response", responseMapping);
        
        return JSON.toJSONString(dslExpression, true);
    }
}
