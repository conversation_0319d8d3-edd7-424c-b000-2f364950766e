package com.yxt.safecenter.service.model.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务接口表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Data
public class SafeInterfaceSimpleBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 服务名
     */
    private String applicationName;

    /**
     * 请求方式
     */
    private String apiWay;

    /**
     * path路径
     */
    private String apiPath;

    /**
     * 权限相关 json
     */
    private String authInfo;

    /**
     * 是否统一加解密 ENC-加密 DENC-解密 ENC_DENC-加密解密
     */
    private String encryptionRequired;

    /**
     * 状态 上线中-ONLINE_RUNNING 已上线-ONLINE 下线中-OFFLINE_RUNNING 已下线-OFFLINE
     */
    private String status;

    /**
     * 参数转换规则配置
     */
    private String transformRules;
}
