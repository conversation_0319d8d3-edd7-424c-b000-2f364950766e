package com.yxt.safecenter.service.manager.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.service.model.bo.InterfaceQueryBO;
import com.yxt.safecenter.service.model.bo.SafeInterfaceBO;
import com.yxt.safecenter.service.model.bo.SafeInterfaceOnlineBO;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.common.model.enums.InterfaceStatusEnum;
import com.yxt.safecenter.service.model.bo.SafeInterfaceSimpleBO;
import com.yxt.safecenter.service.model.redis.BizRedisCacheGroup;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 服务接口表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface SafeInterfaceManager {

    /**
     * 批量插入
     *
     * @param onlineBOList
     * @param applicationName
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_APPLICATION, key = "#applicationName")
    void saveBatch(List<SafeInterfaceOnlineBO> onlineBOList, String applicationName);

    /**
     * 批量更新
     *
     * @param onlineBOList
     * @param applicationName
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_APPLICATION, key = "#applicationName")
    void upBatch(List<SafeInterfaceOnlineBO> onlineBOList, String applicationName);

    /**
     * 更新基本信息
     *
     * @param updateReq
     * @param applicationName
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_APPLICATION, key = "#applicationName")
    void upBase(SafeInterfaceUpReq updateReq, String applicationName);

    /**
     * 批量更新基本信息
     *
     * @param updateReqs 集合
     * @param applicationName 服务名
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_APPLICATION, key = "#applicationName")
    void upBaseBatch(Collection<SafeInterfaceUpReq> updateReqs, String applicationName);

    /**
     * 状态更新
     *
     * @param ids
     * @param statusEnum
     * @param applicationName
     */
    @CacheEvict(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_APPLICATION, key = "#applicationName")
    void upStatus(List<Long> ids, InterfaceStatusEnum statusEnum, String applicationName);

    /**
     * 根据系统查询接口信息
     *
     * @param applicationName
     * @return
     */
//    @Cacheable(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_APPLICATION, key = "#applicationName", unless = "#result == null")
    List<SafeInterfaceBO> listByApplicationName(String applicationName);

    /**
     * 根据系统查询接口信息（simple）
     *
     * @param applicationName 应用名称
     * @return
     */
    @Cacheable(cacheNames = BizRedisCacheGroup.SAFE_INTER_FACE_APPLICATION, key = "#applicationName", unless = "#result == null")
    List<SafeInterfaceSimpleBO> listSimpleByApplicationName(String applicationName);

    int deleteByApplicationNameAndClass(String applicationName,List<String> apiClassList);

    /**
     * 分页查询
     *
     * @param queryReq
     * @return
     */
    PageDTO<SafeInterfaceResp> page(SafeInterfaceQueryReq queryReq);

    /**
     * 根据id
     *
     * @param id
     */
    SafeInterfaceBO getById(Long id);

    /**
     * 列表
     *
     * @param ids
     * @return
     */
    List<SafeInterfaceBO> listByIds(List<Long> ids);

    /**
     * 按id、服务名查询接口
     * @param bo
     * @return
     */
    List<SafeInterfaceBO> listByIdApplicationNames(InterfaceQueryBO bo);

    /**
     * 应用名称集合
     *
     * @return
     * @param applicationName
     */
    List<String> listApplicationName(String applicationName);

}
