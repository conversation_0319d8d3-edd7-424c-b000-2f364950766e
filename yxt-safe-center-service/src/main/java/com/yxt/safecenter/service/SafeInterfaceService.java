package com.yxt.safecenter.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.safecenter.common.model.dto.resp.AuthSelectTreeResp;
import com.yxt.safecenter.service.model.bo.SafeInterfaceOnlineBO;
import com.yxt.safecenter.service.model.bo.SafeInterfaceEBO;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.service.model.bo.SafeInterfaceSimpleBO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年02月07日 10:53
 */
public interface SafeInterfaceService {

    /**
     * 服务接口上报
     *
     * @param onlineBOList
     */
    void online(List<? extends SafeInterfaceOnlineBO> onlineBOList);

    /**
     * 获取所有应用名称
     *
     * @return
     * @param applicationName  可模糊查询
     */
    List<String> listApplicationName(String applicationName);

    /**
     * 分页查询
     *
     * @param queryReq
     * @return
     */
    PageDTO<SafeInterfaceResp> page(String empCode, SafeInterfaceQueryReq queryReq);

    /**
     * 导出分页查询
     *
     * @param queryReq
     * @return
     */
    PageDTO<SafeInterfaceEBO> pageExport(String empCode, SafeInterfaceQueryReq queryReq);

    /**
     * 查询应用所有接口
     * @param applicationName 应用名
     * @return list
     */
    List<SafeInterfaceResp> listByApplicationName(String applicationName);

    /**
     * 查询应用所有接口(simple)
     * @param applicationName 应用名
     * @return list
     */
    List<SafeInterfaceSimpleBO> listSimpleByApplicationName(String applicationName);

    /**
     * 更新接口信息
     *
     * @param updateReq
     */
    void upBase(SafeInterfaceUpReq updateReq);

    /**
     * 批量更新接口信息
     *
     * @param updateReqs 集合
     * @param applicationName 服务名
     */
    void upBaseBatch(Collection<SafeInterfaceUpReq> updateReqs, String applicationName);

    /**
     * 接口上下架
     *
     * @param ids
     * @param up
     */
    void upStatus(List<Long> ids, Boolean up);

    /**
     * 导入接口信息
     *
     * @param imports
     */
    void imports(List<SafeInterfaceEBO> imports);

    /**
     * 按id查询有权限的接口信息
     *
     * @param empCode 工号
     * @param interfaceIds 接口集合
     * @return
     */
    List<SafeInterfaceResp> listByIds(String empCode, Set<Long> interfaceIds);

    /**
     * 获取鉴权选择信息
     * @return json list
     */
    List<AuthSelectTreeResp> getAuthSelectInfo();
}
