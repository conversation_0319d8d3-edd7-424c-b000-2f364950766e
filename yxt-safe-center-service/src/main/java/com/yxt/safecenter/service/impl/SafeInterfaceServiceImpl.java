package com.yxt.safecenter.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Sets;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.safecenter.common.model.dto.AuthInfoDTO;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceQueryReq;
import com.yxt.safecenter.common.model.dto.req.SafeInterfaceUpReq;
import com.yxt.safecenter.common.model.dto.resp.AuthSelectTreeResp;
import com.yxt.safecenter.common.model.dto.resp.SafeInterfaceResp;
import com.yxt.safecenter.common.model.enums.*;
import com.yxt.safecenter.service.SafeInterfaceService;
import com.yxt.safecenter.service.UserProjectService;
import com.yxt.safecenter.service.manager.iface.SafeInterfaceManager;
import com.yxt.safecenter.service.model.bo.*;
import com.yxt.safecenter.service.utils.BeanUtil;
import com.yxt.safecenter.service.utils.JsonUtil;
import com.yxt.safecenter.service.utils.PageDtoUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * .
 *
 * <AUTHOR>
 * @date 2025年01月07日 10:53
 */
@Service
public class SafeInterfaceServiceImpl implements SafeInterfaceService {

    @Autowired
    private SafeInterfaceManager safeInterfaceManager;
    @Resource
    private UserProjectService userProjectService;
    @Value("${biz.interface.project-auth:true}")
    private Boolean enableProjectAuth;

    @Value("${biz.interface.project-auth.ignore.emp-codes:}")
    private List<String> ignoreEmpCodes;

    @Override
    public void online(List<? extends SafeInterfaceOnlineBO> onlineBOList) {
        Map<String, List<SafeInterfaceOnlineBO>> applicationMapLists = onlineBOList.stream()
                .collect(Collectors.groupingBy(SafeInterfaceOnlineBO::getApplicationName));
        for (Map.Entry<String, List<SafeInterfaceOnlineBO>> entry : applicationMapLists.entrySet()) {

            List<SafeInterfaceOnlineBO> saveOnlineBOList = new ArrayList<>();
            List<SafeInterfaceOnlineBO> upOnlineBOList = new ArrayList<>();

            // 判断是新增还是更新
            buildOnlineBO(entry, saveOnlineBOList, upOnlineBOList);

            // 新增
            safeInterfaceManager.saveBatch(saveOnlineBOList, entry.getKey());
            // 更新
            safeInterfaceManager.upBatch(upOnlineBOList, entry.getKey());
        }
    }

    @Override
    public List<String> listApplicationName(String applicationName) {
        return safeInterfaceManager.listApplicationName(applicationName);
    }

    @Override
    public PageDTO<SafeInterfaceResp> page(String empCode, SafeInterfaceQueryReq queryReq) {
        if (enableProjectAuth && !ignoreEmpCodes.contains(empCode)) {
            // 用户项目权限
            Set<String> userApplicationNameSet = userProjectService.getUserApplicationNameSet(empCode, queryReq.getApplicationName());
            if (CollectionUtils.isEmpty(userApplicationNameSet)) {
                // 加分页参数
                return PageDtoUtil.emptyPageDTO(queryReq);
            }
            // 已将服务名在查询权限时过滤
            queryReq.setApplicationName(null);
            queryReq.setApplicationNames(userApplicationNameSet);
        }
        return safeInterfaceManager.page(queryReq);
    }

    @Override
    public PageDTO<SafeInterfaceEBO> pageExport(String empCode, SafeInterfaceQueryReq queryReq) {
        if (enableProjectAuth && !ignoreEmpCodes.contains(empCode)) {
            // 用户项目权限
            Set<String> userApplicationNameSet = userProjectService.getUserApplicationNameSet(empCode, queryReq.getApplicationName());
            if (CollectionUtils.isEmpty(userApplicationNameSet)) {
                // 加分页参数
                return PageDtoUtil.emptyPageDTO(queryReq);
            }
            // 已将服务名在查询权限时过滤
            queryReq.setApplicationName(null);
            queryReq.setApplicationNames(userApplicationNameSet);
        }
        PageDTO<SafeInterfaceResp> page = safeInterfaceManager.page(queryReq);
        return PageDtoUtil.convert(page, BeanUtil.copyList(page.getData(), SafeInterfaceEBO.class));
    }

    @Override
    public List<SafeInterfaceResp> listByApplicationName(String applicationName) {
        return BeanUtil.copyList(safeInterfaceManager.listByApplicationName(applicationName), SafeInterfaceResp.class);
    }

    @Override
    public List<SafeInterfaceSimpleBO> listSimpleByApplicationName(String applicationName) {
        return safeInterfaceManager.listSimpleByApplicationName(applicationName);
    }

    @Override
    public void upBase(SafeInterfaceUpReq updateReq) {
        // 清理缓存
        SafeInterfaceBO safeInterfaceBO = safeInterfaceManager.getById(updateReq.getId());
        if (safeInterfaceBO == null) {
            return;
        }
        List<AuthInfoDTO> authInfo = updateReq.getAuthInfo();
        if (CollectionUtil.isNotEmpty(authInfo)) {
            validateAuthInfo(authInfo);
        }
        safeInterfaceManager.upBase(updateReq, safeInterfaceBO.getApplicationName());
    }

    @Override
    public void upBaseBatch(Collection<SafeInterfaceUpReq> updateReqs, String applicationName) {
        safeInterfaceManager.upBaseBatch(updateReqs, applicationName);
    }

    @Override
    public void upStatus(List<Long> ids, Boolean up) {
        List<SafeInterfaceBO> safeInterfaceBOS = safeInterfaceManager.listByIds(ids);
        if (up) {
            // 判断核心信息是否配置
            for (SafeInterfaceBO safeInterfaceBO : safeInterfaceBOS) {
                String authInfo = safeInterfaceBO.getAuthInfo();
                if (StringUtils.isEmpty(authInfo) || CollectionUtil.isEmpty(JSONArray.parseArray(authInfo, AuthInfoDTO.class))) {
                    throw new YxtBizException(String.join(",",
                            safeInterfaceBO.getApiClass(),
                            safeInterfaceBO.getApiMethod()) + "没有配置网关权限信息，不可上线");
                }
                List<AuthInfoDTO> authInfoDTOS = JSONArray.parseArray(authInfo, AuthInfoDTO.class);
                validateAuthInfo(authInfoDTOS);
            }
        }
        Map<String, List<SafeInterfaceBO>> stringListMap = safeInterfaceBOS
                .stream().collect(Collectors.groupingBy(SafeInterfaceBO::getApplicationName));
        for (Map.Entry<String, List<SafeInterfaceBO>> entry : stringListMap.entrySet()) {
            List<Long> collect = entry.getValue().stream().map(SafeInterfaceBO::getId).collect(Collectors.toList());
            safeInterfaceManager.upStatus(collect, up ? InterfaceStatusEnum.ONLINE : InterfaceStatusEnum.OFFLINE, entry.getKey());
        }
    }

    @Override
    public void imports(List<SafeInterfaceEBO> imports) {
        List<SafeInterfaceBO> safeInterfaceBOS = BeanUtil.copyList(imports, SafeInterfaceBO.class);
        for (SafeInterfaceBO anImport : safeInterfaceBOS) {
            if (!JsonUtil.isValidJsonArray(anImport.getApiWay())) {
                throw new YxtBizException("API 请求方式必须为json数组:" + anImport.getApiWay());
            }
            if (!JsonUtil.isValidJsonArray(anImport.getApiPath())) {
                throw new YxtBizException("API 路径必须为json数组:" + anImport.getApiPath());
            }
            String status = anImport.getStatus();
            if (StringUtils.isNotEmpty(status) && InterfaceStatusEnum.getDescByName(status) == null) {
                throw new YxtBizException("状态 参数异常:" + status);
            }
            String authInfo = anImport.getAuthInfo();
            if (StringUtils.isNotEmpty(authInfo)) {
                if (!JsonUtil.isValidJsonArray(authInfo)) {
                    throw new YxtBizException("网关权限信息必须为json数组:" + authInfo);
                }
                validateAuthInfo(JSONArray.parseArray(authInfo, AuthInfoDTO.class));
            } else if (StringUtils.isNotEmpty(status) && !InterfaceStatusEnum.OFFLINE.name().equals(status)) {
                throw new YxtBizException("网关必须为json数组:" + anImport.getAuthInfo());
            }
            if (!StringUtils.isEmpty(anImport.getEncryptionRequired()) && EncryptionRequiredEnum.getByName(anImport.getEncryptionRequired()) == null) {
                throw new YxtBizException("加解密 参数异常:" + anImport.getEncryptionRequired());
            }
        }
        online(safeInterfaceBOS);
    }

    @Override
    public List<SafeInterfaceResp> listByIds(String empCode, Set<Long> interfaceIds) {
        // 用户项目权限
        Set<String> userApplicationNameSet = userProjectService.getUserApplicationNameSet(empCode, null);
        if (CollectionUtils.isEmpty(userApplicationNameSet)) {
            // 加分页参数
            return Collections.emptyList();
        }
        InterfaceQueryBO queryBO = new InterfaceQueryBO();
        queryBO.setIds(interfaceIds);
        queryBO.setApplicationNames(userApplicationNameSet);
        return BeanUtil.copyList(safeInterfaceManager.listByIdApplicationNames(queryBO), SafeInterfaceResp.class);
    }

    @Override
    public List<AuthSelectTreeResp> getAuthSelectInfo() {
        return Arrays.stream(AuthGatewayEnum.values())
                .map(authEnum -> authEnum.convertToDTO(authEnum))
                .collect(Collectors.toList());
    }

    private void buildOnlineBO(Map.Entry<String, List<SafeInterfaceOnlineBO>> entry, List<SafeInterfaceOnlineBO> saveOnlineBOList, List<SafeInterfaceOnlineBO> upOnlineBOList) {
        // 判断是新增还是更新
        Map<String, List<SafeInterfaceOnlineBO>> newKeySafeInterfaceBOs = entry.getValue()
                .stream().collect(Collectors.groupingBy(
                        t -> t.getApiClass() + "_" + t.getApiMethod() + "_" + t.getApiWay()
                ));

        // db数据
        List<SafeInterfaceBO> dbSafeInterfaceBOS = safeInterfaceManager.listByApplicationName(entry.getKey());
        Map<String, List<SafeInterfaceBO>> dbKeySafeInterfaceBOs = dbSafeInterfaceBOS
                .stream().collect(Collectors.groupingBy(
                        t -> t.getApiClass() + "_" + t.getApiMethod() + "_" + t.getApiWay()
                ));
        Iterator<Map.Entry<String, List<SafeInterfaceBO>>> dbIterator =
                dbKeySafeInterfaceBOs.entrySet().iterator();

        // 优先根据  api类 + api方法 + 请求方式 判断
        while (dbIterator.hasNext()) {
            Map.Entry<String, List<SafeInterfaceBO>> next = dbIterator.next();
            List<SafeInterfaceOnlineBO> newSafeInterfaceBOS = newKeySafeInterfaceBOs.get(next.getKey());
            if (CollectionUtil.isEmpty(newSafeInterfaceBOS)) {
                continue;
            }
            apiPathCheck(next.getValue(), newSafeInterfaceBOS, saveOnlineBOList, upOnlineBOList);
        }
        // 其次 比较path 只要包含一个就认为比对成功
        List<SafeInterfaceBO> dbSafeInterfaces = dbKeySafeInterfaceBOs.values().stream().flatMap(List::stream).collect(Collectors.toList());
        List<SafeInterfaceOnlineBO> newOnlineBOS = newKeySafeInterfaceBOs.values().stream().flatMap(List::stream).collect(Collectors.toList());
        apiPathCheck(dbSafeInterfaces, newOnlineBOS, saveOnlineBOList, upOnlineBOList);
    }

    private void apiPathCheck(List<SafeInterfaceBO> dbSafeInterfaceBOS,
                              List<SafeInterfaceOnlineBO> newOnlineBOS,
                              List<SafeInterfaceOnlineBO> saveOnlineBOList, List<SafeInterfaceOnlineBO> upOnlineBOList) {
        Iterator<SafeInterfaceBO> dbIterator = dbSafeInterfaceBOS.iterator();
        while (dbIterator.hasNext()) {
            SafeInterfaceBO dbInterfaceBO = dbIterator.next();
            // 比较path 只要包含一个就认为比对成功
            boolean checkSuccess = false;
            Set<String> dbApiPath = Sets.newHashSet(JSONArray.parseArray(dbInterfaceBO.getApiPath()).toJavaList(String.class));
            Iterator<SafeInterfaceOnlineBO> newIterator = newOnlineBOS.iterator();
            while (newIterator.hasNext()) {
                SafeInterfaceOnlineBO newSafeInterfaceBO = newIterator.next();
                for (Object newApiPath : JSONArray.parseArray(newSafeInterfaceBO.getApiPath())) {
                    if (dbApiPath.contains(newApiPath)) {
                        checkSuccess = true;
                        newSafeInterfaceBO.setId(dbInterfaceBO.getId());
                        upOnlineBOList.add(newSafeInterfaceBO);
                        // 匹配成功
                        newIterator.remove();
                        break;
                    }
                }
                if (checkSuccess) {
                    break;
                }
            }
            if (checkSuccess) {
                dbIterator.remove();
            }
        }
        saveOnlineBOList.addAll(newOnlineBOS);
    }


    private void validateAuthInfo(List<AuthInfoDTO> authInfo) {
        // 是否重复
        if (authInfo.stream().map(AuthInfoDTO::getAuthGateway).collect(Collectors.toSet()).size() != authInfo.size()) {
            throw new YxtBizException("网关配置重复:" + authInfo);
        }
        for (AuthInfoDTO authInfoDTO : authInfo) {
            if (authInfoDTO.getAuthGateway() == null) {
                throw new YxtBizException("网关 参数异常:" + authInfoDTO.getAuthGateway());
            }
            List<AuthModeEnum> thModes = authInfoDTO.getAuthGateway().getAuthModes();
            if (CollectionUtil.isNotEmpty(thModes)) {
                if (authInfoDTO.getAuthMode() == null || !thModes.contains(authInfoDTO.getAuthMode())) {
                    throw new YxtBizException("鉴权方式 参数异常:" + authInfoDTO.getAuthMode());
                }
            } else if (authInfoDTO.getAuthMode() != null) {
                throw new YxtBizException(String.format("网关%s,没有鉴权方式可选:%s", authInfoDTO.getAuthGateway(), authInfoDTO.getAuthMode()));
            }
//            List<RbacValidationEnum> rbacValidations = authInfoDTO.getAuthGateway().getRbacValidations();
//            if (CollectionUtil.isNotEmpty(rbacValidations)){
//                if ( (authInfoDTO.getRbacValidation() == null || !rbacValidations.contains(authInfoDTO.getRbacValidation()))) {
//                    throw new YxtBizException("RBAC权限 参数异常:" + authInfoDTO.getRbacValidation());
//                }
//            } else if (authInfoDTO.getRbacValidation() != null) {
//                throw new YxtBizException(String.format("网关%s,没有RBAC权限可选:%s", authInfoDTO.getAuthGateway(), authInfoDTO.getAuthMode()));
//            }
        }
    }

}
